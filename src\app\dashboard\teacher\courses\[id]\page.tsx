'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';
import { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';
import { transformApiCourseToWizardData, transformWizardDataToApiUpdate, ApiCourse, ApiModule, ApiChapter, ApiQuiz, ApiQuestion } from '@/utils/course-data-transformer';

export default function CourseEditPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const router = useRouter();
  
  const [courseData, setCourseData] = useState<CourseData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      fetchCourseData();
    }
  }, [id]);

  const fetchCourseData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const user = authStorage.getUser();
      if (!user) {
        setError('Please log in to edit course');
        return;
      }

      // Fetch course data
      const response = await fetch(`/api/courses/${id}?teacherId=${user.id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch course data');
      }

      if (!data.success || !data.course) {
        throw new Error('Course not found');
      }

      const apiCourse: ApiCourse = data.course;

      // Extract module quizzes and final exam from API response
      const moduleQuizzes: ApiQuiz[] = apiCourse.moduleQuizzes || [];
      const finalExam: ApiQuiz | undefined = moduleQuizzes.find(quiz => quiz.quizType === 'final');
      const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');

      // Transform API data to wizard format
      const transformedData = transformApiCourseToWizardData(apiCourse, actualModuleQuizzes, finalExam);
      setCourseData(transformedData);
      
    } catch (error) {
      console.error('Error fetching course:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch course data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCourseUpdate = async (updatedCourseData: CourseData) => {
    try {
      setIsUpdating(true);
      
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to update course');
        return;
      }

      // Transform wizard data back to API format
      const updateData = transformWizardDataToApiUpdate(updatedCourseData, id);
      
      // Update course basic info
      const courseResponse = await fetch(`/api/courses/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: updateData.name,
          description: updateData.description,
          courseCode: updateData.courseCode,
          type: updateData.type,
          startDate: updateData.startDate,
          endDate: updateData.endDate,
          teacherId: user.id
        })
      });

      if (!courseResponse.ok) {
        const errorData = await courseResponse.json();
        throw new Error(errorData.error || 'Failed to update course');
      }

      // Update modules, chapters, and quizzes
      for (const moduleData of updateData.modules) {
        if (moduleData.id) {
          // Update existing module
          await fetch(`/api/modules/${moduleData.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: moduleData.name,
              description: moduleData.description,
              orderIndex: moduleData.orderIndex,
              teacherId: user.id
            })
          });
        } else {
          // Create new module
          const newModuleResponse = await fetch('/api/modules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: moduleData.name,
              description: moduleData.description,
              orderIndex: moduleData.orderIndex,
              courseId: parseInt(id),
              teacherId: user.id
            })
          });
          
          if (newModuleResponse.ok) {
            const newModule = await newModuleResponse.json();
            moduleData.id = newModule.module.id;
          }
        }

        // Update chapters
        for (const chapter of moduleData.chapters) {
          if (chapter.id) {
            // Update existing chapter
            await fetch(`/api/chapters/${chapter.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: chapter.name,
                content: chapter.content,
                orderIndex: chapter.orderIndex,
                teacherId: user.id
              })
            });
          } else {
            // Create new chapter
            const newChapterResponse = await fetch('/api/chapters', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: chapter.name,
                content: chapter.content,
                orderIndex: chapter.orderIndex,
                moduleId: moduleData.id,
                teacherId: user.id
              })
            });
            
            if (newChapterResponse.ok) {
              const newChapter = await newChapterResponse.json();
              chapter.id = newChapter.chapter.id;
            }
          }

          // Update chapter quiz if exists
          if (chapter.quiz) {
            if (chapter.quiz.id) {
              // Update existing quiz
              await fetch(`/api/quizzes/${chapter.quiz.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  name: chapter.quiz.name,
                  description: chapter.quiz.description,
                  quizType: 'chapter',
                  timeLimit: chapter.quiz.timeLimit,
                  minimumScore: chapter.quiz.minimumScore,
                  teacherId: user.id,
                  questions: chapter.quiz.questions
                })
              });
            } else {
              // Create new quiz
              await fetch('/api/quizzes', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  name: chapter.quiz.name,
                  description: chapter.quiz.description,
                  quizType: 'chapter',
                  timeLimit: chapter.quiz.timeLimit,
                  minimumScore: chapter.quiz.minimumScore,
                  chapterId: chapter.id,
                  teacherId: user.id,
                  questions: chapter.quiz.questions
                })
              });
            }
          }
        }

        // Update module quiz if exists
        if (moduleData.quiz) {
          if (moduleData.quiz.id) {
            // Update existing quiz
            await fetch(`/api/quizzes/${moduleData.quiz.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: moduleData.quiz.name,
                description: moduleData.quiz.description,
                quizType: 'module',
                timeLimit: moduleData.quiz.timeLimit,
                minimumScore: moduleData.quiz.minimumScore,
                teacherId: user.id,
                questions: moduleData.quiz.questions
              })
            });
          } else {
            // Create new quiz
            await fetch('/api/quizzes', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: moduleData.quiz.name,
                description: moduleData.quiz.description,
                quizType: 'module',
                timeLimit: moduleData.quiz.timeLimit,
                minimumScore: moduleData.quiz.minimumScore,
                moduleId: moduleData.id,
                teacherId: user.id,
                questions: moduleData.quiz.questions
              })
            });
          }
        }
      }

      // Update final exam if exists
      if (updateData.finalExam) {
        if (updateData.finalExam.id) {
          // Update existing final exam
          await fetch(`/api/quizzes/${updateData.finalExam.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: updateData.finalExam.name,
              description: updateData.finalExam.description,
              quizType: 'final',
              timeLimit: updateData.finalExam.timeLimit,
              minimumScore: updateData.finalExam.minimumScore,
              teacherId: user.id,
              questions: updateData.finalExam.questions
            })
          });
        } else {
          // Create new final exam
          await fetch('/api/quizzes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: updateData.finalExam.name,
              description: updateData.finalExam.description,
              quizType: 'final',
              timeLimit: updateData.finalExam.timeLimit,
              minimumScore: updateData.finalExam.minimumScore,
              courseId: parseInt(id),
              teacherId: user.id,
              questions: updateData.finalExam.questions
            })
          });
        }
      }

      toast.success('Course updated successfully!');
      router.push('/dashboard/teacher/courses');
      
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update course');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/teacher/courses');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Link href="/dashboard/teacher/courses">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Edit Course</h1>
          <p className="text-muted-foreground">Update course information and content</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Course
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchCourseData} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!courseData) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Link href="/dashboard/teacher/courses">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Edit Course</h1>
          <p className="text-muted-foreground">Update course information and content</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Course Not Found</CardTitle>
            <CardDescription>The requested course could not be found.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/dashboard/teacher/courses">
              <Button>Back to Courses</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link href="/dashboard/teacher/courses">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courses
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Edit Course</h1>
        <p className="text-muted-foreground">Update course information and content</p>
      </div>

      <CourseCreationWizard
        initialData={courseData}
        onComplete={handleCourseUpdate}
        onCancel={handleCancel}
      />
    </div>
  );
}